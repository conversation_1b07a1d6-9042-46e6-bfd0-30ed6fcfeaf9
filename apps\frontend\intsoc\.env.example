# Frontend IntelligenceSoc Environment Variables

NEXT_PORT=3000 # Port to run frontend server on

# Client-side Configuration
API_BASE_URL=http://localhost:3000 # Frontend server API URL (exposed to client)
WS_URL=ws://localhost:4001/ws # Backend WebSocket URL (exposed to client)
USE_API_PROXY=true # Proxy requests to server (recommended true)
API_TIMEOUT=10000 
ENABLE_API_CACHE=false
API_CACHE_TIMEOUT=60000
DEBUG_API=false

# Server-side Configuration
BACKEND_API_URL=http://localhost:4001 # URL of the backend API server
BACKEND_API_TIMEOUT=10000 # timeout for server API requests in milliseconds
BACKEND_API_MAX_RETRIES=3 # maximum number of retries for failed API requests
BACKEND_API_RETRY_DELAY=1000 # delay between retries for failed API requests in milliseconds

NODE_ENV=development # Node environment (development, production)

APPLICATION_NAME="IntSOC Cyber"
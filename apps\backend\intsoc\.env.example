# Server Configuration
NODE_ENV=production
PORT=4001
DOMAIN=localhost

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Redis Timeout Settings (in milliseconds) - Optimized for localhost
REDIS_CONNECT_TIMEOUT=2000
REDIS_COMMAND_TIMEOUT=1000

# External API Configuration
EXTERNAL_THREATS_API=http://localhost:5000/api/v1/threats
EXTERNAL_THREATS_WS=ws://localhost:5000/api/v1/threats
EXTERNAL_ML_API=http://localhost:5000/api/v1/deployments
EXTERNAL_ML_WS=ws://localhost:5000/api/v1/deployments/data
